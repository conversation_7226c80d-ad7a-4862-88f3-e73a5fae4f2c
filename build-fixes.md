# Build Error Fix Plan

This document outlines all the build errors and proposed fixes. Please edit this file to indicate which fixes you want implemented by changing `[ ]` to `[x]` for the items you want fixed.

## 1. Missing Hook Exports

Several hooks are being imported but not exported from '@/lib/hooks':

- [x] **useCollaborationRealtime** ✅ FIXED

  - Files affected:
    - app/(user)/[username]/collaboration/hub/page.tsx
    - app/(user)/[username]/collaboration/projects/[projectId]/client-realtime.tsx
    - app/(user)/[username]/collaboration/projects/client-realtime.tsx
  - Fix implemented: The useCollaborationRealtime hook has been properly implemented in the use-supabase.ts file and is being correctly imported from '@/lib/hooks' in all affected files. The TypeScript errors related to missing collaboration types have also been fixed by adding the proper imports.

- [x] **useLawyersExtended**

  - Files affected:
    - app/(user)/[username]/lawyer/clients/page.tsx
    - app/(user)/[username]/lawyer/dashboard/page.tsx
    - components/lawyer/ClientManagement.tsx
  - Proposed fix: check if it exists in the use-supabase.tsx file if not then create it and export it from there dont create any new supabase hooks outside the use-supabase.tsx file and dont export anything from the hooks/index.ts filr we are already exporting everything from the use-supabase hook so we will make the changes in the use-supabase file and export it there and then make sure all the files have their hooks imported from '@/lib/hooks' insted of anything else

- [x] **useSettingsHook**

  - Files affected:
    - app/(user)/[username]/settings/security/page.tsx
  - Proposed fix: check if it exists in the use-supabase.tsx file if not then create it and export it from there dont create any new supabase hooks outside the use-supabase.tsx file and dont export anything from the hooks/index.ts filr we are already exporting everything from the use-supabase hook so we will make the changes in the use-supabase file and export it there and then make sure all the files have their hooks imported from '@/lib/hooks' insted of anything else

- [x] **useBookingsRealtime**
  - Files affected:
    - components/lawyer/ConsultationDashboard.tsx
  - Proposed fix: check if it exists in the use-supabase.tsx file if not then create it and export it from there dont create any new supabase hooks outside the use-supabase.tsx file and dont export anything from the hooks/index.ts filr we are already exporting everything from the use-supabase hook so we will make the changes in the use-supabase file and export it there and then make sure all the files have their hooks imported from '@/lib/hooks' insted of anything else

## 2. React No-Unescaped-Entities Errors

Multiple files have unescaped entities (quotes and apostrophes) that need to be properly escaped:

- [x] **Lawyer Consultation Pages**

  - Files affected:
    - app/(user)/[username]/lawyer/consultations/page-realtime.tsx
    - app/(user)/[username]/lawyer/consultations/page.tsx
  - Proposed fix: Replace `'` with `&apos;`

- [x] **Lawyer Profile Pages**

  - Files affected:
    - app/(user)/[username]/lawyer/profile/[id]/page.tsx
  - Proposed fix: Replace `'` with `&apos;`

- [x] **Organization Pages**

  - Files affected:
    - app/(user)/[username]/organizations/new/page.tsx
    - app/(user)/[username]/organizations/[id]/members/[memberId]/edit-role/page.tsx
    - app/(user)/[username]/organizations/[id]/page.tsx
    - app/(user)/[username]/organizations/[id]/settings/page.tsx
    - app/(user)/[username]/organizations/[id]/teams/new/page.tsx
  - Proposed fix: Replace `'` with `&apos;`

- [x] **Auth Pages**

  - Files affected:
    - app/auth/forgot-password/page.tsx
    - app/auth-error/page.tsx
  - Proposed fix: Replace `'` with `&apos;` and `"` with `&quot;`

- [x] **Document Sharing Components**

  - Files affected:
    - app/shared/[token]/page.tsx
    - components/documents/shared/PasswordAccessForm.tsx
    - components/documents/shared/PinAccessForm.tsx
    - components/documents/sheets/DocumentShareSheet.tsx
  - Proposed fix: Replace `"` with `&quot;`

- [x] **Lawyer Components**

  - Files affected:
    - components/documents/panels/LawyerConsultationPanel.tsx
    - components/lawyer/ClientManagement.tsx
    - components/lawyer/ConsultationDashboard.tsx
    - components/lawyer/DocumentReviewSubmission.tsx
    - components/lawyer/RecurringConsultationsList.tsx
  - Proposed fix: Replace `'` with `&apos;`

- [x] **Onboarding Components**

  - Files affected:
    - components/forms/auth/create-account.tsx
    - components/onboarding/ProfileSetupStep.tsx
    - components/onboarding/RoleSetupStep.tsx
  - Proposed fix: Replace `'` with `&apos;`

- [x] **Organization Components**

  - Files affected:
    - components/organizations/CreateOrganizationDialog.tsx
    - components/organizations/CreateTeamDialog.tsx
  - Proposed fix: Replace `'` with `&apos;`

- [x] **Reports Components**

  - Files affected:
    - components/reports/SavedReportsList.tsx
  - Proposed fix: Replace `"` with `&quot;`

- [x] **Settings Components**

  - Files affected:
    - components/settings/PushNotificationSettings.tsx
  - Proposed fix: Replace `'` with `&apos;`

- [x] **Email Templates**
  - Files affected:
    - lib/emails/templates/document-notification-email.tsx
    - lib/emails/templates/reset-password-email.tsx
    - lib/emails/templates/verification-email.tsx
  - Proposed fix: Replace `'` with `&apos;`

## 3. React Hooks Rules Violations

Some components are violating React hooks rules:

- [x] **DocumentPreview.tsx**

  - Issue: Conditional `useMemo` calls
  - Proposed fix: Restructure to ensure hooks are called unconditionally

- [x] **shared/[token]/page.tsx**
  - Issue: Improper hook usage in handlePasswordSubmit function
  - Proposed fix: Move hook outside of the function or rename function to follow React hook naming convention

## 4. Missing Display Names

Some components are missing display names:

- [x] **DocumentLoadingState.tsx**

  - Issue: Components missing display names
  - Proposed fix: Add display names to components

- [x] **performance-optimizations.tsx**
  - Issue: Component missing display name
  - Proposed fix: Add display name to component

## 5. ARIA Role Issues

One component has an ARIA role issue:

- [x] **RoleSetupStep.tsx**
  - Issue: The attribute aria-disabled is not supported by the role form
  - Proposed fix: Remove aria-disabled attribute or change the role

## 6. ESLint Anonymous Default Export Warning

One file has an anonymous default export:

- [x] **broadcast-helper.ts**
  - Issue: Anonymous default export
  - Proposed fix: Assign object to variable before exporting

Please check the boxes for the fixes you want implemented, save the file, and I'll proceed with those changes.
