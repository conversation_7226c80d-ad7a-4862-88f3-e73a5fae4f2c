# NotAMess Forms Project Structure

## Directory Structure

```
notamess_forms/
├── app/
│   ├── (landing)/
│   │   ├── auth/
│   │   │   └── confirm/
│   │   ├── create-account/
│   │   ├── login/
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── (user)/
│   │   ├── [username]/
│   │   │   ├── documents/
│   │   │   │   ├── [id]/
│   │   │   │   │   ├── edit/
│   │   │   │   │   ├── share/
│   │   │   │   │   ├── client.tsx
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── create/
│   │   │   │   ├── client.tsx
│   │   │   │   └── page.tsx
│   │   │   ├── settings/
│   │   │   ├── messages/
│   │   │   ├── teams/
│   │   │   ├── network/
│   │   │   ├── collaboration/
│   │   │   ├── lawyer/
│   │   │   ├── onboarding/
│   │   │   ├── client.tsx
│   │   │   └── page.tsx
│   │   └── layout.tsx
│   ├── api/
│   │   ├── documents/
│   │   ├── auth/
│   │   └── webhooks/
│   ├── auth/
│   │   ├── callback/
│   │   └── oauth/
│   ├── auth-error/
│   ├── not-found-handler/
│   ├── shared/
│   │   └── documents/
│   ├── layout.tsx
│   ├── not-found.tsx
│   ├── globals.css
│   └── [assets]
├── components/
│   ├── auth/
│   ├── calendar/
│   ├── collaboration/
│   ├── documents/
│   │   ├── editor/
│   │   │   ├── RichTextEditor.tsx
│   │   │   └── SectionBasedEditor.tsx
│   │   ├── signature/
│   │   ├── sharing/
│   │   └── templates/
│   ├── enterprise/
│   ├── form/
│   ├── forms/
│   ├── lawyer/
│   ├── layout/
│   ├── layouts/
│   ├── mobile/
│   ├── onboarding/
│   ├── organizations/
│   ├── providers/
│   ├── reports/
│   ├── settings/
│   ├── ui/
│   │   ├── alert-dialog.tsx
│   │   ├── aspect-ratio.tsx
│   │   ├── button.tsx
│   │   ├── calendar.tsx
│   │   ├── card.tsx
│   │   ├── checkbox.tsx
│   │   ├── dialog.tsx
│   │   ├── dropdown-menu.tsx
│   │   ├── form.tsx
│   │   ├── input.tsx
│   │   ├── label.tsx
│   │   ├── popover.tsx
│   │   ├── scroll-area.tsx
│   │   ├── select.tsx
│   │   ├── skeleton.tsx
│   │   ├── sonner.tsx
│   │   ├── switch.tsx
│   │   ├── tabs.tsx
│   │   ├── textarea.tsx
│   │   └── tooltip.tsx
│   ├── ux/
│   │   └── icons/
│   ├── DocumentPreview.tsx
│   └── page-header.tsx
├── lib/
│   ├── components/
│   ├── constants/
│   │   ├── schemas/
│   │   ├── index.ts
│   │   └── [various constants]
│   ├── contexts/
│   ├── emails/
│   ├── forms/
│   │   └── templates/
│   ├── hooks/
│   │   ├── realtime/
│   │   ├── index.ts
│   │   ├── use-mobile.ts
│   │   ├── use-supabase.ts
│   │   ├── usePermissions.ts
│   │   ├── useScroll.ts
│   │   └── useTemplateLoader.ts
│   ├── imgs/
│   ├── middleware/
│   ├── providers/
│   ├── services/
│   │   ├── avatar-service.ts
│   │   └── [other services]
│   ├── store/
│   │   ├── clear-stores.ts
│   │   ├── user.ts
│   │   ├── waitlist.ts
│   │   ├── is-editing.ts
│   │   └── dynamicNav.ts
│   ├── supabase/
│   │   ├── auth/
│   │   │   ├── auth-service.ts
│   │   │   ├── getUser.ts
│   │   │   ├── middleware.ts
│   │   │   └── session.ts
│   │   ├── db/
│   │   │   ├── migrations/
│   │   │   └── seed/
│   │   ├── realtime/
│   │   ├── client.ts
│   │   ├── database-types.ts
│   │   └── server-client.ts
│   ├── templates/
│   ├── types/
│   │   └── database-modules.ts
│   ├── user/
│   │   └── simple-user.ts
│   └── utils/
│       └── [utility functions]
├── public/
├── styles/
├── types/
├── memory-bank/
├── scripts/
├── tasks/
└── Configuration Files
    ├── .env
    ├── .env.local
    ├── .eslintrc.json
    ├── .prettierrc
    ├── components.json
    ├── middleware.ts
    ├── next-env.d.ts
    ├── next.config.mjs
    ├── postcss.config.mjs
    └── tsconfig.json

## Core Components

### Authentication & Authorization
- Complete Supabase integration with client/server auth handlers
- Session management with middleware protection
- Role-based access control (user, lawyer)
- Social login (Google)
- Email/password authentication
- Profile management

### Document Management System
- Document creation, editing, and viewing
- Section-based document editor with rich text formatting
- Document sharing and collaboration
- Document templates system
- Document versioning and history tracking
- Document activities and audit trail
- Electronic signatures
- Export to PDF/DOCX

### Lawyer Consultation System
- Lawyer profiles and availability management
- Consultation scheduling
- Messaging system
- Document review workflow
- Calendar integration

### State Management
- Zustand store implementation with multiple slices:
  - User store (authentication state, profile)
  - Waitlist store
  - Editing state store
  - Dynamic navigation store
- Context providers for global state
- Centralized hooks system in use-supabase.ts

### Database & API
- Supabase PostgreSQL database with RLS policies
- Comprehensive database schema with migrations
- Type-safe database access with generated types
- Realtime subscriptions for collaborative features
- Centralized data access through hooks

### UI Components
The project uses a comprehensive UI component library built with shadcn/ui, including:
- Form elements (inputs, textareas, selects, checkboxes)
- Dialog and alert components
- Card and layout components
- Navigation elements (tabs, dropdown menus)
- Data display components (tables, lists)
- Loading states and skeletons
- Toast notifications with Sonner
- Interactive components (calendar, tabs)

### Custom Hooks
- use-supabase.ts: Centralized data access
- usePermissions.ts: Permission checking
- useTemplateLoader.ts: Document template loading
- useScroll.ts: Scroll handling
- use-mobile.ts: Mobile detection

### Utilities & Services
- Avatar service for profile image management
- Authentication service
- Date/time handling utilities
- Document export service
- Notification system
- Email templates

## Project Configuration
- Next.js 15+ with App Router
- TypeScript configuration
- ESLint and Prettier for code quality
- Tailwind CSS with PostCSS
- Environment variables
- Middleware for request handling and authentication
- Bun package manager
```
