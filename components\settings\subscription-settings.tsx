'use client';

import { useState } from 'react';
import { useSettings } from '@/lib/hooks';
import { SubscriptionInfo } from '@/lib/types/database-modules';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import {
  AlertCircle,
  Check,
  CreditCard,
  FileText,
  Loader2,
  Lock,
  Shield,
  Users,
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

// Define the plan structure based on the actual database schema
interface PlanInfo {
  id: string;
  plan: string;
  document_limit: number | null;
  storage_limit: number | null;
  collaborators_limit: number | null;
  lawyer_consultations_limit: number | null;
  smart_contracts_limit: number | null;
  features: {
    advanced_templates?: boolean;
    smart_contract_deployment?: boolean;
    lawyer_consultations?: boolean;
    priority_support?: boolean;
  } | null;
}

interface SubscriptionSettingsProps {
  subscription: SubscriptionInfo | null | undefined;
  availablePlans: PlanInfo[] | undefined;
}

export function SubscriptionSettings({
  subscription,
  availablePlans,
}: SubscriptionSettingsProps) {
  const { loading, formatBytes } = useSettings();

  const [isUpgradeDialogOpen, setIsUpgradeDialogOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>('');

  const handleUpgrade = async () => {
    if (!selectedPlan || !subscription) return;

    toast.promise(
      Promise.resolve(), // Placeholder for actual subscription update
      {
        loading: 'Updating subscription...',
        success: 'Subscription updated successfully!',
        error: 'Failed to update subscription',
      }
    );

    setIsUpgradeDialogOpen(false);
  };

  const handleToggleAutoRenew = async () => {
    if (!subscription) return;

    toast.promise(
      Promise.resolve(), // Placeholder for actual auto-renew toggle
      {
        loading: 'Updating auto-renewal...',
        success: 'Auto-renewal setting updated!',
        error: 'Failed to update auto-renewal setting',
      }
    );
  };

  if (!subscription || !availablePlans) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-1/3" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-24 w-full" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-1/3" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-2 w-3/4" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-2 w-1/2" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { subscription: sub, limits, usage } = subscription;

  // Calculate usage percentages
  const documentsPercentage = limits.document_limit
    ? Math.min(100, (usage.documents_count / limits.document_limit) * 100)
    : 0;

  const storagePercentage = limits.storage_limit
    ? Math.min(100, (usage.storage_used / limits.storage_limit) * 100)
    : 0;

  const collaboratorsPercentage = limits.collaborators_limit
    ? Math.min(
        100,
        (usage.collaborators_count / limits.collaborators_limit) * 100
      )
    : 0;

  const contractsPercentage = limits.smart_contracts_limit
    ? Math.min(
        100,
        (usage.smart_contracts_count / limits.smart_contracts_limit) * 100
      )
    : 0;

  const lawyersPercentage = limits.lawyer_consultations_limit
    ? Math.min(
        100,
        (usage.lawyer_consultations_count / limits.lawyer_consultations_limit) *
          100
      )
    : 0;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>Current Plan</CardTitle>
              <CardDescription>
                Your current subscription plan and billing details
              </CardDescription>
            </div>
            <Dialog
              open={isUpgradeDialogOpen}
              onOpenChange={setIsUpgradeDialogOpen}
            >
              <DialogTrigger asChild>
                <Button>Upgrade Plan</Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Upgrade Your Plan</DialogTitle>
                  <DialogDescription>
                    Choose a plan that best fits your needs
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  {availablePlans.map((plan) => (
                    <div
                      key={plan.id}
                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        selectedPlan === plan.plan
                          ? 'border-blue-500 bg-blue-50'
                          : 'hover:border-neutral-300'
                      }`}
                      onClick={() => setSelectedPlan(plan.plan)}
                    >
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-semibold text-lg capitalize">
                          {plan.plan}
                        </h3>
                        {selectedPlan === plan.plan && (
                          <Check className="h-5 w-5 text-blue-500" />
                        )}
                      </div>
                      <div className="text-sm text-neutral-600 mb-3">
                        {plan.plan === 'free'
                          ? 'Basic features for personal use'
                          : plan.plan === 'standard'
                            ? 'Advanced features for professionals'
                            : 'Complete solution for businesses'}
                      </div>
                      <div className="text-2xl font-bold mb-4">
                        {plan.plan === 'free'
                          ? 'Free'
                          : plan.plan === 'standard'
                            ? '$19.99/month'
                            : 'Contact Sales'}
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-neutral-500" />
                          <span>
                            {plan.document_limit === null
                              ? 'Unlimited documents'
                              : `${plan.document_limit} documents`}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-neutral-500" />
                          <span>
                            {plan.collaborators_limit === null
                              ? 'Unlimited collaborators'
                              : `${plan.collaborators_limit} collaborators per document`}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-neutral-500" />
                          <span>
                            {plan.smart_contracts_limit === null
                              ? 'Unlimited smart contracts'
                              : `${plan.smart_contracts_limit} smart contracts`}
                          </span>
                        </div>
                        {plan.features && (
                          <>
                            {plan.features.advanced_templates && (
                              <div className="flex items-center gap-2">
                                <Check className="h-4 w-4 text-green-500" />
                                <span>Advanced templates</span>
                              </div>
                            )}
                            {plan.features.smart_contract_deployment && (
                              <div className="flex items-center gap-2">
                                <Check className="h-4 w-4 text-green-500" />
                                <span>Smart contract deployment</span>
                              </div>
                            )}
                            {plan.features.lawyer_consultations && (
                              <div className="flex items-center gap-2">
                                <Check className="h-4 w-4 text-green-500" />
                                <span>Lawyer consultations</span>
                              </div>
                            )}
                            {plan.features.priority_support && (
                              <div className="flex items-center gap-2">
                                <Check className="h-4 w-4 text-green-500" />
                                <span>Priority support</span>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsUpgradeDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleUpgrade}
                    disabled={!selectedPlan || loading}
                  >
                    {loading && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Upgrade to {selectedPlan || 'Selected Plan'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="bg-neutral-50 border rounded-lg p-4">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold capitalize">
                  {sub.plan} Plan
                </h3>
                <p className="text-sm text-neutral-500">
                  {sub.plan === 'free'
                    ? 'Basic features for personal use'
                    : sub.plan === 'standard'
                      ? 'Advanced features for professionals'
                      : 'Complete solution for businesses'}
                </p>
              </div>
              <div className="mt-2 sm:mt-0">
                <span className="inline-block px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                  {sub.status.charAt(0).toUpperCase() + sub.status.slice(1)}
                </span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 mb-4">
              <div className="flex-1">
                <p className="text-sm font-medium mb-1">Started On</p>
                <p className="text-sm">
                  {new Date(sub.start_date).toLocaleDateString()}
                </p>
              </div>
              {sub.end_date && (
                <div className="flex-1">
                  <p className="text-sm font-medium mb-1">Expires On</p>
                  <p className="text-sm">
                    {new Date(sub.end_date).toLocaleDateString()}
                  </p>
                </div>
              )}
              <div className="flex-1">
                <p className="text-sm font-medium mb-1">Billing</p>
                <p className="text-sm">
                  {sub.plan === 'free'
                    ? 'Free'
                    : sub.plan === 'standard'
                      ? '$19.99/month'
                      : 'Custom pricing'}
                </p>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-neutral-500" />
                <span className="text-sm">
                  {sub.payment_method
                    ? `${sub.payment_method.brand} ending in ${sub.payment_method.last4}`
                    : 'No payment method on file'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm">Auto-renew</span>
                <Switch
                  checked={sub.auto_renew}
                  onCheckedChange={handleToggleAutoRenew}
                  disabled={loading || sub.plan === 'free'}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Usage & Limits</CardTitle>
          <CardDescription>
            Monitor your current usage and plan limits
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Documents</span>
              <span>
                {usage.documents_count} / {limits.document_limit || 'Unlimited'}
              </span>
            </div>
            <Progress value={documentsPercentage} className="h-2" />
            <p className="text-xs text-neutral-500">
              {limits.document_limit
                ? `${limits.document_limit - usage.documents_count} documents remaining`
                : 'Unlimited documents available'}
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Storage</span>
              <span>
                {formatBytes(usage.storage_used)} /{' '}
                {limits.storage_limit
                  ? formatBytes(limits.storage_limit)
                  : 'Unlimited'}
              </span>
            </div>
            <Progress value={storagePercentage} className="h-2" />
            <p className="text-xs text-neutral-500">
              {limits.storage_limit
                ? `${formatBytes(limits.storage_limit - usage.storage_used)} remaining`
                : 'Unlimited storage available'}
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Collaborators</span>
              <span>
                {usage.collaborators_count} /{' '}
                {limits.collaborators_limit || 'Unlimited'}
              </span>
            </div>
            <Progress value={collaboratorsPercentage} className="h-2" />
            <p className="text-xs text-neutral-500">
              {limits.collaborators_limit
                ? `${
                    limits.collaborators_limit - usage.collaborators_count
                  } collaborators remaining`
                : 'Unlimited collaborators available'}
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Smart Contracts</span>
              <span>
                {usage.smart_contracts_count} /{' '}
                {limits.smart_contracts_limit || 'Unlimited'}
              </span>
            </div>
            <Progress value={contractsPercentage} className="h-2" />
            <p className="text-xs text-neutral-500">
              {limits.smart_contracts_limit
                ? `${
                    limits.smart_contracts_limit - usage.smart_contracts_count
                  } contracts remaining`
                : 'Unlimited contracts available'}
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Lawyer Consultations</span>
              <span>
                {usage.lawyer_consultations_count} /{' '}
                {limits.lawyer_consultations_limit || 'Unlimited'}
              </span>
            </div>
            <Progress value={lawyersPercentage} className="h-2" />
            <p className="text-xs text-neutral-500">
              {limits.lawyer_consultations_limit
                ? `${
                    limits.lawyer_consultations_limit -
                    usage.lawyer_consultations_count
                  } consultations remaining`
                : 'Unlimited consultations available'}
            </p>
          </div>

          {sub.plan !== 'enterprise' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
              <div className="flex gap-3">
                <AlertCircle className="h-5 w-5 text-blue-500 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-blue-800 mb-1">
                    Need more resources?
                  </h4>
                  <p className="text-sm text-blue-700">
                    Upgrade your plan to get more documents, storage, and
                    features.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2 bg-white"
                    onClick={() => setIsUpgradeDialogOpen(true)}
                  >
                    View Plans
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {limits.features && (
        <Card>
          <CardHeader>
            <CardTitle>Features</CardTitle>
            <CardDescription>
              Features included in your current plan
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <div
                  className={`p-2 rounded-full ${
                    limits.features.advanced_templates
                      ? 'bg-green-100'
                      : 'bg-neutral-100'
                  }`}
                >
                  <FileText
                    className={`h-5 w-5 ${
                      limits.features.advanced_templates
                        ? 'text-green-600'
                        : 'text-neutral-400'
                    }`}
                  />
                </div>
                <div>
                  <h4 className="font-medium">Advanced Templates</h4>
                  <p className="text-sm text-neutral-500">
                    Access to premium document templates
                  </p>
                </div>
                {!limits.features.advanced_templates && (
                  <Lock className="h-4 w-4 text-neutral-400 ml-auto" />
                )}
              </div>

              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <div
                  className={`p-2 rounded-full ${
                    limits.features.smart_contract_deployment
                      ? 'bg-green-100'
                      : 'bg-neutral-100'
                  }`}
                >
                  <Shield
                    className={`h-5 w-5 ${
                      limits.features.smart_contract_deployment
                        ? 'text-green-600'
                        : 'text-neutral-400'
                    }`}
                  />
                </div>
                <div>
                  <h4 className="font-medium">Smart Contract Deployment</h4>
                  <p className="text-sm text-neutral-500">
                    Deploy contracts to blockchain networks
                  </p>
                </div>
                {!limits.features.smart_contract_deployment && (
                  <Lock className="h-4 w-4 text-neutral-400 ml-auto" />
                )}
              </div>

              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <div
                  className={`p-2 rounded-full ${
                    limits.features.lawyer_consultations
                      ? 'bg-green-100'
                      : 'bg-neutral-100'
                  }`}
                >
                  <Users
                    className={`h-5 w-5 ${
                      limits.features.lawyer_consultations
                        ? 'text-green-600'
                        : 'text-neutral-400'
                    }`}
                  />
                </div>
                <div>
                  <h4 className="font-medium">Lawyer Consultations</h4>
                  <p className="text-sm text-neutral-500">
                    Get legal advice from professionals
                  </p>
                </div>
                {!limits.features.lawyer_consultations && (
                  <Lock className="h-4 w-4 text-neutral-400 ml-auto" />
                )}
              </div>

              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <div
                  className={`p-2 rounded-full ${
                    limits.features.priority_support
                      ? 'bg-green-100'
                      : 'bg-neutral-100'
                  }`}
                >
                  <AlertCircle
                    className={`h-5 w-5 ${
                      limits.features.priority_support
                        ? 'text-green-600'
                        : 'text-neutral-400'
                    }`}
                  />
                </div>
                <div>
                  <h4 className="font-medium">Priority Support</h4>
                  <p className="text-sm text-neutral-500">
                    Get faster responses from our support team
                  </p>
                </div>
                {!limits.features.priority_support && (
                  <Lock className="h-4 w-4 text-neutral-400 ml-auto" />
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
